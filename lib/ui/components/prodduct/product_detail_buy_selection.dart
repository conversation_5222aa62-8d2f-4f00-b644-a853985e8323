import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ProductDetailBuySelection extends StatelessWidget {
  const ProductDetailBuySelection({
    super.key,
    required this.text,
    required this.amount,
    this.isSelected = false,
    this.showDiscount = false,
    this.discountText,
    this.onTap,
  });

  final String text;
  final String amount;
  final bool isSelected;
  final bool showDiscount;
  final String? discountText;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Sizer.radius(16),
        ),
        decoration: BoxDecoration(
            border: Border.all(
          color: AppColors.blackBD,
        )),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  CustomRadioBtn(
                    isSelected: isSelected,
                    // onTap: () {},
                  ),
                  const XBox(8),
                  Text(
                    text,
                    style: AppTypography.text14.copyWith(),
                  ),
                  const XBox(12),
                  if (showDiscount && discountText != null)
                    DiscountTag(
                      text: discountText ?? "",
                    ),
                ],
              ),
            ),
            Text(
              amount,
              style: AppTypography.text18.copyWith(
                color: AppColors.primaryBlack,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
