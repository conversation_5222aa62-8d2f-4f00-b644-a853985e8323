import 'package:bottle_king_mobile/core/core.dart';

class DiscountTag extends StatelessWidget {
  const DiscountTag({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.radius(9),
        horizontal: Sizer.width(14),
      ),
      color: AppColors.red2F,
      child: Text(
        text,
        style: AppTypography.text12.copyWith(
          color: AppColors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
