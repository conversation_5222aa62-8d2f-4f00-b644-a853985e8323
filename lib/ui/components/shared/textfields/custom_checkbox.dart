import 'package:bottle_king_mobile/core/core.dart';

class CustomCheckbox extends StatelessWidget {
  const CustomCheckbox({
    super.key,
    this.onTap,
    this.isSelected = false,
  });

  final VoidCallback? onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: isSelected ? Sizer.width(22) : Sizer.width(20),
        height: isSelected ? Sizer.height(22) : Sizer.height(20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.black : AppColors.transparent,
          border: Border.all(color: AppColors.grayDD),
          borderRadius: BorderRadius.circular(Sizer.radius(4)),
        ),
        child:
            isSelected ? const Icon(Icons.check, color: AppColors.white) : null,
      ),
    );
  }
}

class CustomRadioBtn extends StatelessWidget {
  const CustomRadioBtn({
    super.key,
    this.onTap,
    this.isSelected = false,
  });

  final VoidCallback? onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: Sizer.width(isSelected ? 18 : 16),
        height: Sizer.height(isSelected ? 18 : 16),
        padding: EdgeInsets.all(
          Sizer.radius(2),
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.black),
          borderRadius: BorderRadius.circular(Sizer.radius(20)),
        ),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.black : AppColors.white,
            borderRadius: BorderRadius.circular(Sizer.radius(20)),
          ),
        ),
      ),
    );
  }
}
