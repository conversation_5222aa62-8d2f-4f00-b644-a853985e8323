import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ShopProductScreen extends ConsumerStatefulWidget {
  const ShopProductScreen({
    super.key,
    required this.category,
  });

  final String category;

  @override
  ConsumerState<ShopProductScreen> createState() => _ShopProductScreenState();
}

class _ShopProductScreenState extends ConsumerState<ShopProductScreen> {
  final ScrollController _scrollController = ScrollController();
  final List<ProductModel> _productsByCaterory = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // Store current filter parameters for pagination
  Map<String, dynamic> _currentFilterParams = {};

  @override
  void initState() {
    super.initState();

    final prodVm = ref.read(productVm);
    // Add listener to load more data when user reaches the end of the list
    _scrollController.addListener(() {
      if (_scrollController.position.maxScrollExtent ==
          _scrollController.offset) {
        if (prodVm.totalNumber >= prodVm.limit) {
          _paginateWithCurrentFilters();
          printty("Paginating");
        } else {
          printty("No more data");
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) async {
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
      });
      ref.read(productVm).clearCategorySearchResults();
      return;
    }

    setState(() {
      _isSearching = true;
    });

    await ref.read(productVm).searchProductsByCategory(
          query: query.trim(),
          category: widget.category.isEmpty ? null : widget.category,
        );
  }

  _init() async {
    // Initialize with base category filter
    _currentFilterParams = {
      'category': widget.category,
    };

    final r = await ref.read(productVm).getProductsByCategoryFilter(
          category: widget.category,
        );

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
    }
  }

  _applyFilters() async {
    final filterParams = ref.read(filterVm).getFilterParams();

    // Combine base category with filter params
    final params = {
      'category': widget.category,
      ...filterParams,
    };

    // Store current filter parameters for pagination
    _currentFilterParams = params;

    // Only include price if at least one price value is valid (not null and not 0)
    String? priceParam;
    final minPrice = params['min_price'];
    final maxPrice = params['max_price'];
    
    bool hasValidMinPrice = minPrice != null && 
        minPrice.toString() != '0' && 
        minPrice.toString() != 'null' &&
        minPrice.toString().isNotEmpty;
        
    bool hasValidMaxPrice = maxPrice != null && 
        maxPrice.toString() != '0' && 
        maxPrice.toString() != 'null' &&
        maxPrice.toString().isNotEmpty;
    
    if (hasValidMinPrice || hasValidMaxPrice) {
      String minPriceStr = hasValidMinPrice ? minPrice.toString() : '';
      String maxPriceStr = hasValidMaxPrice ? maxPrice.toString() : '';
      priceParam = "$minPriceStr,$maxPriceStr";
    }

    final r = await ref.read(productVm).getProductsByCategoryFilter(
          category: params['category'],
          isBestSeller: params['extra_category'] == 'bestsellers',
          isRecommended: params['extra_category'] == 'recommended',
          isNewArrival: params['extra_category'] == 'newarrival',
          hasDiscount: params['extra_category'] == 'discount',
          sortBy: params['sortBy'],
          cartonSize: params['size'],
          price: priceParam,
        );

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
      setState(() {});
    }
  }

  _paginateWithCurrentFilters() async {
    // Only include price if at least one price value is valid (not null and not 0)
    String? priceParam;
    final minPrice = _currentFilterParams['min_price'];
    final maxPrice = _currentFilterParams['max_price'];
    
    bool hasValidMinPrice = minPrice != null && 
        minPrice.toString() != '0' && 
        minPrice.toString() != 'null' &&
        minPrice.toString().isNotEmpty;
        
    bool hasValidMaxPrice = maxPrice != null && 
        maxPrice.toString() != '0' && 
        maxPrice.toString() != 'null' &&
        maxPrice.toString().isNotEmpty;
    
    if (hasValidMinPrice || hasValidMaxPrice) {
      String minPriceStr = hasValidMinPrice ? minPrice.toString() : '';
      String maxPriceStr = hasValidMaxPrice ? maxPrice.toString() : '';
      priceParam = "$minPriceStr,$maxPriceStr";
    }

    await ref.read(productVm).getProductsByCategoryFilter(
          category: _currentFilterParams['category'],
          isBestSeller: _currentFilterParams['extra_category'] == 'bestsellers',
          isRecommended:
              _currentFilterParams['extra_category'] == 'recommended',
          isNewArrival: _currentFilterParams['extra_category'] == 'newarrival',
          hasDiscount: _currentFilterParams['extra_category'] == 'discount',
          sortBy: _currentFilterParams['sortBy'],
          cartonSize: _currentFilterParams['size'],
          price: priceParam,
          isFirstCall: false,
        );
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return CartFloatingActionButton(
      bottomPosition: 40,
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(80)),
          child: Container(
            // color: AppColors.red,
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        ref.read(filterVm).clearAllFilters();
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: EdgeInsets.all(Sizer.radius(4)),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: Sizer.height(20),
                          color: AppColors.black70,
                        ),
                      ),
                    ),
                    const XBox(8),
                    Expanded(
                      child: CustomTextField(
                        controller: _searchController,
                        hintText: 'Find your favourite drinks',
                        fillColor: AppColors.grayF6,
                        hideBorder: true,
                        borderRadius: 0,
                        onChanged: _onSearchChanged,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.all(6),
                          child: Container(
                            width: Sizer.width(40),
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(10),
                              vertical: Sizer.height(10),
                            ),
                            color: AppColors.primaryBlack,
                            child: SvgPicture.asset(AppSvgs.search),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const YBox(10),
              ],
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Consumer(
                builder: (context, ref, child) {
                  final filterRef = ref.watch(filterVm);

                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    // mainAxisAlignment: filterRef.currentFilter.hasActiveFilters
                    //     ? MainAxisAlignment.spaceBetween
                    //     : MainAxisAlignment.start,
                    children: [
                      ShopFilter(
                        text: filterRef.getFilterDisplayText(),
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: UnifiedFilterModal(
                              onApply: () {
                                // Apply filters to product list
                                _applyFilters();
                              },
                            ),
                          );
                        },
                      ),
                      // Show the active filter here
                      if (filterRef.currentFilter.hasActiveFilters) ...[
                        const XBox(12),
                        InkWell(
                          onTap: () {
                            ref.read(filterVm).clearAllFilters();
                            _applyFilters();
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.radius(10),
                              vertical: Sizer.height(10),
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.blackBD),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Icon(
                              Icons.clear,
                              size: Sizer.height(20),
                              color: AppColors.black70,
                            ),
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
            const YBox(6),
            Expanded(
              child: Builder(builder: (context) {
                // Determine which data to show and loading state
                final bool isLoading = _isSearching
                    ? productRef.busy(categorySearchState)
                    : productRef.busy(productsByCategoryFilter);

                final List<ProductModel> productsToShow = _isSearching
                    ? productRef.categorySearchResults
                    : productRef.productsByCaterory;

                if (isLoading) {
                  return const ProductShimmer();
                }

                if (productsToShow.isEmpty) {
                  return Center(
                    child: Text(_isSearching
                        ? "No products found for your search"
                        : "No products found for ${widget.category}"),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    await _applyFilters();
                  },
                  child: ListView(
                    controller: _scrollController,
                    padding: EdgeInsets.only(
                      top: Sizer.height(10),
                      bottom: Sizer.height(250),
                    ),
                    children: [
                      GridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.only(
                          left: Sizer.width(16),
                          right: Sizer.width(16),
                        ),
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        crossAxisCount: 2,
                        childAspectRatio: 0.64,
                        children: List.generate(
                          productsToShow.length,
                          (i) => HomeProductCard(
                            productModel: productsToShow[i],
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.productDetailsScreen,
                                arguments: productsToShow[i],
                              );
                            },
                          ),
                        ),
                      ),
                      if (productRef.busy(paginateState))
                        const ProductShimmer(count: 4)
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class ProductShimmer extends StatelessWidget {
  const ProductShimmer({
    super.key,
    this.count = 20,
  });

  final int count;

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      // physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        top: Sizer.height(10),
      ),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      crossAxisCount: 2,
      childAspectRatio: 0.68,
      children: List.generate(
        count,
        (i) => Skeletonizer(
          enabled: true,
          child: HomeProductCard(
            productModel: ProductModel(
              name: "Glenfiddich 18yrs",
              volume: "75cl",
              unitPrice: 379500,
              category: "BEST SELLER",
            ),
          ),
        ),
      ),
    );
  }
}
