import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ShopScreen extends ConsumerStatefulWidget {
  const ShopScreen({super.key});

  @override
  ConsumerState<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends ConsumerState<ShopScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  _getCategories() {
    ref.read(productVm).getProductCategories();
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return CartFloatingActionButton(
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(60)),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            color: AppColors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.searchProductScreen,
                    );
                  },
                  child: AbsorbPointer(
                    child: CustomTextField(
                      hintText: 'Find your favourite drinks',
                      fillColor: AppColors.grayF6,
                      hideBorder: true,
                      borderRadius: 0,
                      suffixIcon: Padding(
                        padding: const EdgeInsets.all(6),
                        child: Container(
                          width: Sizer.width(40),
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(10),
                            vertical: Sizer.height(10),
                          ),
                          color: AppColors.primaryBlack,
                          child: SvgPicture.asset(AppSvgs.search),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        body: Column(
          children: [
            const YBox(20),
            Container(
              height: Sizer.height(42),
              width: Sizer.screenWidth,
              decoration: BoxDecoration(
                color: AppColors.greyF7,
                border: Border.all(
                  width: 1,
                  color: AppColors.grayF0,
                ),
              ),
              margin: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(6),
                vertical: Sizer.height(4),
              ),
              child: TabBar(
                splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                physics: const NeverScrollableScrollPhysics(),
                onTap: (int value) {
                  setState(() {
                    selectedIndex = value;
                  });
                },
                dividerColor: Colors.transparent,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(Sizer.radius(8)),
                  color: AppColors.white,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: AppColors.primaryBlack,
                automaticIndicatorColorAdjustment: true,
                labelStyle: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: AppTypography.text14.copyWith(
                  color: AppColors.black70,
                  fontWeight: FontWeight.w500,
                ),
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Shop categories'),
                  Tab(text: 'Discover'),
                ],
              ),
            ),
            const YBox(10),
            Expanded(
              child: Builder(
                builder: (context) {
                  if (productRef.isBusy) {
                    return GridView.count(
                      shrinkWrap: true,
                      crossAxisCount: 2,
                      mainAxisSpacing: Sizer.width(20),
                      crossAxisSpacing: Sizer.width(20),
                      childAspectRatio: 1.5,
                      padding: EdgeInsets.only(
                        left: Sizer.width(16),
                        right: Sizer.width(16),
                        top: Sizer.height(20),
                        bottom: Sizer.height(100),
                      ),
                      children: [
                        for (int i = 0; i < 12; i++)
                          Skeletonizer(
                            enabled: true,
                            child: ShopCategoryCard(
                              title: 'Vodka categories',
                              image: AppImages.cat,
                              onTap: () {},
                            ),
                          ),
                      ],
                    );
                  }
                  if (productRef.productCategories.isEmpty) {
                    return const Center(
                      child: Text("Shop categories is empty"),
                    );
                  }

                  // Add all category at index o

                  return RefreshIndicator(
                    onRefresh: () async {
                      _getCategories();
                    },
                    child: GridView.count(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(
                          left: Sizer.width(16),
                          right: Sizer.width(16),
                          top: Sizer.height(20),
                          bottom: Sizer.height(100),
                        ),
                        crossAxisCount: 2,
                        mainAxisSpacing: Sizer.width(20),
                        crossAxisSpacing: Sizer.width(20),
                        childAspectRatio: 1.5,
                        children: [
                          ShopCategoryCard(
                            title: "All",
                            image: AppImages.all,
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.shopProductScreen,
                                arguments: '',
                              );
                            },
                          ),
                          ...List.generate(
                            productRef.productCategories.length,
                            (i) {
                              final c = productRef.productCategories[i];
                              return ShopCategoryCard(
                                title: c.category ?? '',
                                image: c.image ?? '',
                                onTap: () {
                                  printty("${c.image} ${c.category}");
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.shopProductScreen,
                                    arguments: c.category == 'All'
                                        ? ''
                                        : c.category?.toLowerCase() == 'extras'
                                            ? 'extra'
                                            : c.category?.toLowerCase() ?? '',
                                  );
                                },
                              );
                            },
                          ),
                        ]),
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
