import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ProductDetailsScreen extends ConsumerStatefulWidget {
  const ProductDetailsScreen({
    super.key,
    required this.product,
  });

  final ProductModel product;

  @override
  ConsumerState<ProductDetailsScreen> createState() =>
      _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen> {
  final qtyController = TextEditingController();
  int _productQty = 1;
  bool isFavorite = false;
  bool showProductDetails = false;
  bool showShippingPolicy = false;

  // int selectedIndexTwo = 0;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      qtyController.text = _productQty.toString();
      setState(() {});
    });
  }

  @override
  void dispose() {
    qtyController.dispose();

    super.dispose();
  }

  /// Updates the product quantity and refreshes the UI
  void _updateProductQuantity(int newQty) {
    _productQty = newQty;
    qtyController.text = newQty.toString();
    setState(() {});
  }

  bool get isDiscountAvailable => (widget.product.discountPercentage != null &&
      (widget.product.discountPercentage ?? 0) > 0);
  @override
  Widget build(BuildContext context) {
    printty("Product ID ${widget.product.parentId}");
    return Scaffold(
      backgroundColor: AppColors.white,
      body: RefreshIndicator(
        onRefresh: () async {},
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              surfaceTintColor: AppColors.white,
              leadingWidth: Sizer.width(100),
              leading: Row(
                children: [
                  const XBox(16),
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: EdgeInsets.all(Sizer.radius(4)),
                      decoration: BoxDecoration(
                        color: AppColors.black71.withValues(alpha: 0.09),
                        borderRadius: BorderRadius.circular(Sizer.radius(30)),
                      ),
                      child: Icon(
                        Iconsax.arrow_left_2,
                        size: Sizer.height(24),
                        color: AppColors.primaryBlack,
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                // SvgPicture.asset(
                //   AppSvgs.hearthCircle,
                // ),
                Container(
                  padding: EdgeInsets.all(Sizer.radius(4)),
                  decoration: BoxDecoration(
                    color: AppColors.black71.withValues(alpha: 0.09),
                    borderRadius: BorderRadius.circular(Sizer.radius(30)),
                  ),
                  child: LikeButton(
                    isFavorite: isFavorite,
                    iconColor: AppColors.primaryBlack,
                    splashColor: AppColors.primaryBlack,
                    onChanged: (value) async {
                      isFavorite = value;
                      setState(() {});
                      final cRef = ref.read(cartVm);
                      await cRef.addToCartOrWishlist(
                        productId: widget.product.parentId ?? "",
                        variationId: widget.product.id ?? '',
                        isWishList: true,
                      );
                    },
                  ),
                ),
                const XBox(12),
                InkWell(
                  onTap: () async {
                    final firstVariation = widget.product;
                    SharePlus.instance.share(ShareParams(
                      text:
                          "${widget.product.name ?? ""} \n\n ${EnvConfig.baseUrl}/product/${firstVariation.slug ?? ""}",
                      subject: widget.product.name ?? "",
                      title: widget.product.name ?? "",
                    ));
                  },
                  child: Container(
                      padding: EdgeInsets.all(Sizer.radius(8)),
                      decoration: BoxDecoration(
                        color: AppColors.black71.withValues(alpha: 0.09),
                        borderRadius: BorderRadius.circular(Sizer.radius(30)),
                      ),
                      child: Icon(
                        Iconsax.export_1,
                        size: Sizer.height(24),
                        color: AppColors.primaryBlack,
                      )),
                ),
                const XBox(16),
              ],
              // floating: true,
              pinned: true,
              snap: false,
              backgroundColor: AppColors.greyF7,
              expandedHeight: Sizer.height(360),
              flexibleSpace: FlexibleSpaceBar(
                background: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: Sizer.height(250),
                      child: MyCachedNetworkImage(
                        imageUrl: widget.product.image ?? "",
                      ),
                    ),
                    const YBox(20),
                    if (isDiscountAvailable)
                      Container(
                        alignment: Alignment.topLeft,
                        margin: EdgeInsets.only(
                          left: Sizer.width(16),
                          bottom: Sizer.height(16),
                        ),
                        child: DiscountTag(
                          text:
                              "${widget.product.discountPercentage ?? 0}% OFF",
                        ),
                      ),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Builder(
                builder: (context) {
                  return Column(
                    children: [
                      Container(
                        width: double.infinity,
                        color: Colors.white,
                        padding: EdgeInsets.only(
                          top: Sizer.height(16),
                          left: Sizer.width(16),
                          right: Sizer.width(16),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        (productCategoryHelper(
                                                widget.product.category ?? "")
                                            .categoryNmae
                                            .toUpperCase()),
                                        style: AppTypography.text12.copyWith(
                                          color: AppColors.yellow37,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const YBox(2),
                                      Text(
                                        widget.product.name ?? "",
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTypography.text16.copyWith(
                                          color: AppColors.primaryBlack,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                ShopFilter(
                                  text: "Compare product",
                                  showArrow: false,
                                  textStyle: AppTypography.text14.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                  onTap: () {
                                    ModalWrapper.bottomSheet(
                                        context: context,
                                        widget: const CompareProductModal());
                                  },
                                ),
                              ],
                            ),
                            const YBox(2),
                            Text(
                              widget.product.volume ?? "",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.black70,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const YBox(4),
                            isDiscountAvailable
                                ? Row(
                                    children: [
                                      Text(
                                        "${AppUtils.nairaSymbol}${(AppUtils.formatNumber(
                                          number: widget.product.unitPrice ?? 0,
                                        ))}",
                                        style: AppTypography.text20.copyWith(
                                          color: AppColors.primaryBlack,
                                          fontWeight: FontWeight.w500,
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ),
                                      ),
                                      const XBox(16),
                                      Text(
                                        "${AppUtils.nairaSymbol}${(AppUtils.formatNumber(
                                          number: widget.product.discounts?.unit
                                                  ?.newPrice ??
                                              0,
                                        ))}",
                                        style: AppTypography.text20.copyWith(
                                          color: AppColors.red35,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    "${AppUtils.nairaSymbol}${(AppUtils.formatNumber(
                                      number: widget.product.unitPrice ?? 0,
                                    ))}",
                                    style: AppTypography.text20.copyWith(
                                      color: AppColors.primaryBlack,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                            const YBox(16),
                            ProductDetailBuySelection(
                              text: "Buy 1",
                              amount: _buyExtra(1),
                              isSelected: _productQty == 1,
                              onTap: () {
                                _productQty = 1;
                                qtyController.text = _productQty.toString();
                                setState(() {});
                              },
                            ),
                            const YBox(12),
                            if (extractDigits(widget.product.cartonSize ?? "") >
                                0)
                              ProductDetailBuySelection(
                                text: widget.product.cartonSize ?? "",
                                amount: _buyExtra(extractDigits(
                                    widget.product.cartonSize ?? "")),
                                isSelected: _productQty ==
                                    extractDigits(
                                        widget.product.cartonSize ?? ""),
                                showDiscount: isDiscountAvailable,
                                discountText:
                                    "Save ${widget.product.discounts?.discountsCase?.discountPercentage ?? 0}%",
                                onTap: () {
                                  _productQty = extractDigits(
                                      widget.product.cartonSize ?? "");
                                  qtyController.text = _productQty.toString();
                                  setState(() {});
                                },
                              ),
                            const YBox(20),
                            Row(
                              children: [
                                Text(
                                  'Qty: ',
                                  style: AppTypography.text16.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const XBox(12),
                                CartIncrement(
                                  controller: qtyController,
                                  vPadding: 4,
                                  value: _productQty,
                                  onCrement: () {
                                    _updateProductQuantity(_productQty + 1);
                                  },
                                  onDecrement: () {
                                    if (_productQty > 1) {
                                      _updateProductQuantity(_productQty - 1);
                                    }
                                  },
                                  onQuantityChanged: (newQty) {
                                    _updateProductQuantity(newQty);
                                  },
                                )
                              ],
                            ),
                            const YBox(20),
                            const Divider(
                              thickness: 1,
                              color: AppColors.grayE6,
                              height: 1,
                            ),
                            const YBox(16),
                            Row(
                              children: [
                                Text(
                                  'Product details',
                                  style: AppTypography.text16.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const Spacer(),
                                InkWell(
                                  onTap: () {
                                    showProductDetails = !showProductDetails;
                                    setState(() {});
                                  },
                                  child: Icon(
                                    showProductDetails
                                        ? Icons.minimize
                                        : Icons.add,
                                    color: AppColors.primaryBlack,
                                    size: Sizer.height(20),
                                  ),
                                ),
                              ],
                            ),
                            AnimatedSize(
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                              alignment: Alignment.topCenter,
                              child: showProductDetails
                                  ? Column(
                                      children: [
                                        const YBox(16),
                                        Text(
                                          widget.product.desc ??
                                              "No product details available",
                                          style: AppTypography.text14.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    )
                                  : const SizedBox.shrink(),
                            ),
                            const YBox(20),
                            const Divider(
                              thickness: 1,
                              color: AppColors.grayE6,
                              height: 1,
                            ),
                            const YBox(16),
                            Row(
                              children: [
                                Text(
                                  'Shipping policy',
                                  style: AppTypography.text16.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const Spacer(),
                                InkWell(
                                  onTap: () {
                                    showShippingPolicy = !showShippingPolicy;
                                    setState(() {});
                                  },
                                  child: Icon(
                                    showShippingPolicy
                                        ? Icons.minimize
                                        : Icons.add,
                                    color: AppColors.primaryBlack,
                                    size: Sizer.height(20),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      AnimatedSize(
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                        alignment: Alignment.topCenter,
                        child: showShippingPolicy
                            ? Padding(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(20),
                                  left: Sizer.width(16),
                                  right: Sizer.width(16),
                                ),
                                child: Text(
                                  AppText.policy,
                                  style: AppTypography.text14.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),
                      const YBox(40),
                      ExtraCategoryProduct(
                          category: widget.product.category ?? ''),
                      const YBox(100),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
          top: Sizer.height(10),
          bottom: Sizer.height(30),
        ),
        child: ref.watch(cartVm).isBusy
            ? const BtnLoadState()
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomBtn.solid(
                    onTap: () async {
                      if (ref.read(authVm).user == null) {
                        ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ContinueLoginModal());
                        return;
                      }
                      printty("cart popup productId ${widget.product.id}");
                      final r = await _addProductToCart();

                      handleApiResponse(
                        response: r,
                        onSuccess: () {
                          Navigator.pop(context);
                        },
                      );
                    },
                    online: true,
                    isLoading: ref.watch(cartVm).isBusy,
                    text: "Add to cart",
                  ),
                  const YBox(10),
                  CustomBtn.solid(
                    isOutline: true,
                    textColor: AppColors.primaryBlack,
                    onTap: () async {
                      if (ref.read(authVm).user == null) {
                        ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ContinueLoginModal());
                        return;
                      }
                      final r = await _addProductToCart();

                      handleApiResponse(
                        response: r,
                        onSuccess: () async {
                          // Navigator.pop(context);
                          await ref.read(cartVm).getCart(showLoader: false);
                          Navigator.pushNamed(
                            context,
                            RoutePath.checkoutScreen,
                            arguments: ref.read(cartVm).regularCartProduct,
                          );
                        },
                      );
                    },
                    online: true,
                    text: "Buy now",
                  ),
                ],
              ),
      ),
    );
  }

  Future<ApiResponse> _addProductToCart() async {
    printty("cart popup productId ${widget.product.id}");
    final cRef = ref.read(cartVm);
    final r = await cRef.addToCartOrWishlist(
      productId: widget.product.parentId ?? '',
      variationId: widget.product.id ?? '',
      quantity: _productQty.toString(),
    );
    if (r.success) {
      return r;
    }

    return ApiResponse(
      success: false,
      message: "Something went wrong",
    );
  }

  String _buyExtra(int number) {
    final productAmount = widget.product.unitPrice ?? 0;
    final total = productAmount * number;

    return "${AppUtils.nairaSymbol}${AppUtils.formatNumber(decimalPlaces: 0, number: total)}";
  }
}
