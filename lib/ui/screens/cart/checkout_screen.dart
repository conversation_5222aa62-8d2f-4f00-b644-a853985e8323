import 'package:bottle_king_mobile/lib.dart';

class CheckoutScreen extends ConsumerStatefulWidget {
  const CheckoutScreen({
    super.key,
    required this.cartModel,
  });

  final CartModel cartModel;

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final _phoneC = TextEditingController();
  final _couponC = TextEditingController();
  final _phoneF = FocusNode();
  final _couponF = FocusNode();

  bool _isPhoneReadOnly = true;
  num? _discountValue;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    KeyboardOverlay.addRemoveFocusNode(context, _phoneF);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _phoneC.text = ref.read(authVm).user?.phone ?? "";

      // Set the selected tab based on orderDeliveryType
      final orderDeliveryType = ref.read(addressVm).orderDeliveryType;
      if (orderDeliveryType == OrderDeliveryType.delivery) {
        ref.read(addressVm.notifier).getShippingFee();
      }

      selectedIndex = orderDeliveryType == OrderDeliveryType.delivery ? 0 : 1;
      _tabController.animateTo(selectedIndex);

      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneC.dispose();
    _couponC.dispose();
    _phoneF.dispose();
    _couponF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final addressRef = ref.watch(addressVm);
    final authRef = ref.watch(authVm);
    final cartRef = ref.watch(cartVm);

    printty("bbbbb ${ref.watch(addressVm).orderDeliveryType}");

    return BusyOverlay(
      show: ref.watch(orderViewModel).isBusy,
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(100)),
          child: Container(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: Sizer.height(20),
                          color: AppColors.black70,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: Sizer.width(20)),
                      child: Text(
                        'Checkout',
                        style: AppTypography.text18.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container()
                  ],
                ),
                const YBox(30),
                Container(
                  height: Sizer.height(42),
                  width: Sizer.screenWidth,
                  decoration: BoxDecoration(
                    color: AppColors.greyF7,
                    // borderRadius: BorderRadius.circular(Sizer.radius(12)),
                    border: Border.all(width: 1, color: AppColors.grayF0),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(6),
                    vertical: Sizer.height(4),
                  ),
                  child: TabBar(
                    splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                    physics: const NeverScrollableScrollPhysics(),
                    onTap: (int value) {
                      setState(() {
                        selectedIndex = value;
                        ref.read(addressVm.notifier).setOrderDeliveryType(
                              selectedIndex == 0
                                  ? OrderDeliveryType.delivery
                                  : OrderDeliveryType.pickup,
                            );

                        if (selectedIndex == 0) {
                          ref.read(addressVm.notifier).getShippingFee();
                        }
                      });
                    },
                    dividerColor: Colors.transparent,
                    indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(Sizer.radius(8)),
                      color: AppColors.white,
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: AppColors.primaryBlack,
                    automaticIndicatorColorAdjustment: true,
                    labelStyle: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    unselectedLabelStyle: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontWeight: FontWeight.w500,
                    ),
                    controller: _tabController,
                    tabs: const [
                      Tab(text: 'Delivery'),
                      Tab(text: 'Pick - up'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        body: ListView(
          padding: EdgeInsets.only(
            top: Sizer.height(35),
            bottom: Sizer.height(200),
          ),
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Deliver ${ref.watch(scheduleVmodel).isSchedule ? "on ${ref.watch(scheduleVmodel).formatedScheduleDate}" : "today"}",
                        style: AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ScheduleOrderModal(),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(12),
                            vertical: Sizer.height(8),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blackBD,
                            ),
                          ),
                          child: Text("Schedule",
                              style: AppTypography.text12.copyWith(
                                fontWeight: FontWeight.w600,
                              )),
                        ),
                      ),
                    ],
                  ),
                  const YBox(24),
                  const Divider(
                      color: AppColors.greyF7, thickness: 2, height: 1),
                  const YBox(16),
                  InkWell(
                    onTap: () async {
                      final r = await ModalWrapper.bottomSheet(
                        context: context,
                        widget: const AddressModal(),
                      );

                      if (r is OrderDeliveryType) {
                        selectedIndex = r == OrderDeliveryType.delivery ? 0 : 1;
                        _tabController.animateTo(selectedIndex);
                        setState(() {});
                        if (r == OrderDeliveryType.delivery) {
                          ref.read(addressVm.notifier).getShippingFee();
                        }
                      }
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(AppSvgs.location),
                        const XBox(12),
                        Expanded(
                          child: Text(
                            addressRef.address,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const XBox(10),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(6),
                            vertical: Sizer.height(6),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.greyF7,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(20)),
                          ),
                          child: Icon(
                            Icons.keyboard_arrow_down,
                            size: Sizer.height(24),
                            color: AppColors.black70,
                          ),
                        )
                      ],
                    ),
                  ),
                  const YBox(16),
                  const Divider(
                      color: AppColors.greyF7, thickness: 2, height: 1),
                  // const YBox(16),
                  Row(
                    children: [
                      SvgPicture.asset(AppSvgs.call),
                      Expanded(
                        child: CustomTextField(
                          controller: _phoneC,
                          focusNode: _phoneF,
                          hideBorder: true,
                          hintText: "Add phone number",
                          isReadOnly: _isPhoneReadOnly,
                          keyboardType: KeyboardType.phone,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          _isPhoneReadOnly = !_isPhoneReadOnly;
                          if (!_isPhoneReadOnly) {
                            _phoneF.requestFocus();
                          }
                          setState(() {});
                        },
                        child: SvgPicture.asset(AppSvgs.pen),
                      )
                    ],
                  ),
                ],
              ),
            ),
            const YBox(16),
            Container(
              height: Sizer.height(4),
              width: Sizer.screenWidth,
              color: AppColors.greyF7,
            ),
            const YBox(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Your items",
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(16),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemBuilder: (ctx, i) {
                      final p =
                          widget.cartModel.items?.regularProducts?.items?[i];
                      return OrderProductTile(
                        imageUrl: p?.image ?? "",
                        productName: p?.productName ?? "",
                        quantity: p?.quantity ?? 1,
                        price: p?.price ?? 0,
                      );
                    },
                    separatorBuilder: (_, __) => const YBox(6),
                    itemCount: widget
                            .cartModel.items?.regularProducts?.items?.length ??
                        0,
                  ),
                  const YBox(16),
                  Container(
                    height: Sizer.height(4),
                    width: Sizer.screenWidth,
                    color: AppColors.greyF7,
                  ),
                  const YBox(16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "You have ",
                                    style: AppTypography.text16,
                                  ),
                                  TextSpan(
                                    text: AppUtils.formatNumber(
                                      decimalPlaces: 0,
                                      number: authRef.user?.points ?? 0,
                                    ),
                                    style: AppTypography.text16.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  TextSpan(
                                    text: " points! ",
                                    style: AppTypography.text16,
                                  ),
                                ],
                              ),
                            ),
                            const YBox(4),
                            Text(
                              "Redeem now for a discount!",
                              style: AppTypography.text16,
                            )
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          final r = await ref
                              .read(cartVm.notifier)
                              .rewardGenerateCoupon(authRef.user?.id ?? "");

                          if (r.success) {
                            _couponC.text = r.data?.code ?? "";
                          } else {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: r.message ?? "Something went wrong",
                            );
                          }
                        },
                        child: Container(
                          width: Sizer.width(90),
                          padding: EdgeInsets.symmetric(
                            vertical: Sizer.height(8),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blackBD,
                            ),
                          ),
                          child: cartRef.busy(rewardGenerateCouponState)
                              ? const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    LoaderIcon(size: 20),
                                  ],
                                )
                              : Center(
                                  child: Text(
                                    "Redeem",
                                    style: AppTypography.text14.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                        ),
                      )
                    ],
                  ),
                  const YBox(16),
                  CustomTextField(
                    controller: _couponC,
                    focusNode: _couponF,
                    hintText: "Enter referral or discount code",
                    borderRadius: 0,
                    suffixIcon: InkWell(
                      onTap: () async {
                        final r = await ref
                            .read(cartVm.notifier)
                            .validateCoupon(_couponC.text.trim());
                        if (r.success) {
                          _discountValue = r.data ?? 0;
                          setState(() {});
                        } else {
                          _discountValue = 0;
                          setState(() {});
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                          right: Sizer.width(16),
                          top: Sizer.height(12),
                        ),
                        child: cartRef.busy(validateCouponState)
                            ? const LoaderIcon(size: 30)
                            : Text(
                                "Apply",
                                style: AppTypography.text14.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                    onSubmitted: (p0) => _couponF.unfocus(),
                  ),
                ],
              ),
            ),
            const YBox(16),
            Container(
              height: Sizer.height(4),
              width: Sizer.screenWidth,
              color: AppColors.greyF7,
            ),
            const YBox(16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                children: [
                  CheckoutAmountTile(
                    leftText: "Subtotal",
                    rightText:
                        '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.cartModel.total ?? 0)}',
                  ),
                  if (ref.watch(addressVm).orderDeliveryType ==
                      OrderDeliveryType.delivery)
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const YBox(20),
                        CheckoutAmountTile(
                          leftText: "Delivery fee",
                          rightText:
                              '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: addressRef.deliveryFee?.cost ?? 0)}',
                        ),
                      ],
                    ),
                  if (_discountValue != null && _discountValue! > 0)
                    const YBox(20),
                  if (_discountValue != null && _discountValue! > 0)
                    CheckoutAmountTile(
                      leftText: "Discount (${_discountValue ?? 0}%)",
                      rightText:
                          "-${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: (widget.cartModel.total ?? 0) * (_discountValue ?? 0) / 100)}",
                    ),
                  const YBox(20),
                  CheckoutAmountTile(
                    leftText: "Order total",
                    rightText:
                        '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: orderTotal)}',
                    color: AppColors.primaryBlack,
                  ),
                  const YBox(20),
                ],
              ),
            )
          ],
        ),
        bottomSheet: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            top: Sizer.height(10),
            bottom: Sizer.height(30),
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                width: 1,
                color: AppColors.grayE6,
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomBtn.solid(
                onTap: () async {
                  _placeOrder();
                },
                online: true,
                text: "Place order",
              ),
              const YBox(10),
              CustomBtn.solid(
                onTap: () {
                  _showDeleteConfirmationModal();
                },
                online: true,
                isOutline: true,
                outlineColor: AppColors.blackBD,
                textColor: AppColors.red15,
                text: "Clear order",
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmationModal() {
    final loadingProvider = StateProvider<bool>((ref) => false);
    ModalWrapper.bottomSheet(
      context: context,
      widget: Consumer(
        builder: (context, ref, child) {
          final isLoading = ref.watch(loadingProvider);
          return ConfirmModal(
            title: "Clear order",
            subtitle: "Are you sure you want to clear your order?",
            rightBtnText: "Clear",
            isLoading: isLoading,
            rightBtnTap: () async {
              ref.read(loadingProvider.notifier).state = true;

              try {
                final r = await ref
                    .read(cartVm.notifier)
                    .clearCartOrWishlist(isWishList: false);
                handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      RoutePath.bottomNavScreen,
                      (r) => false,
                      arguments: DashArg(index: 3),
                    );
                  },
                );
              } finally {
                if (context.mounted) {
                  ref.read(loadingProvider.notifier).state = false;
                }
              }
            },
          );
        },
      ),
    );
  }

  _placeOrder() async {
    FocusScope.of(context).unfocus();
    final authRef = ref.read(authVm);
    final scheduleVm = ref.read(scheduleVmodel);
    final r = await ref.read(orderViewModel.notifier).initiateCheckout(args: {
      "cartId": widget.cartModel.id,
      "customerId": authRef.user?.id,
      "deliveryFare": ref.read(addressVm).isDelivery
          ? ref.read(addressVm).deliveryFee?.cost ?? 0
          : 0,
      "address": ref.read(addressVm).address,
      "couponCode": _couponC.text.trim(),
      "date": scheduleVm.scheculeDate?.toIso8601String(),
      "deliveryType": ref.read(addressVm).isDelivery ? "drop-off" : "pick-up",
      "deliveryTime": scheduleVm.scheduleTimeString,
      "addPaper": false,
    });

    printty("initiateCheckout ${r.data}");
    if (r.success) {
      final r = await ref.read(orderViewModel.notifier).initiateTransaction(
            email: authRef.user?.email ?? "",
            amount: orderTotal.toDouble() * 100,
            deliveryType:
                ref.read(addressVm).isDelivery ? "drop-off" : "pick-up",
            cartId: widget.cartModel.id ?? "",
          );
      if (r.success) {
        if (mounted) {
          // Reset schedule delivery type
          scheduleVm.setScheduleDeliveryType(DeliveryType.now);
          Navigator.pushNamed(
            context,
            RoutePath.customWebViewScreen,
            arguments: WebViewArg(
              webURL: r.data["authorization_url"],
              onSucecess: () async {
                // Navigator.pushNamed(context, RoutePath.orderSuccessScreen);

                // After on onSucess, get order by reference
                // await Future.delayed(const Duration(seconds: 1));

                // final createdOrder = await ref
                //     .read(orderViewModel)
                //     .viewSingleOrder(ref: r.data["reference"]);
                if (mounted) {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    RoutePath.bottomNavScreen,
                    (_) => false,
                    arguments: DashArg(index: 2),
                  );

                  Navigator.pushNamed(context, RoutePath.orderDetailsScreen,
                      arguments: OrderDetailsArg(ref: r.data["reference"]));
                  // if (createdOrder.success && createdOrder.data is OrderModel) {
                  //   printty("create order cred: ${createdOrder.data}");

                  // }
                }
              },
            ),
          );
        }
      } else {
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message: r.message ?? "Something went wrong",
        );
      }

      // final result = await PaystackService().makePayment(
      //   context: context,
      //   amount: orderTotal.toDouble(),
      //   email: authRef.user?.email ?? "",
      //   reference: _getReference(),
      // );

      // if (result.success) {
      //   await ref.read(orderViewModel.notifier).finalizeCheckout(args: {
      //     "note": "",
      //     "donate": false,
      //     "addGift": false,
      //     "addPaper": false,
      //     "email": authRef.user?.email ?? "",
      //     "phone": _phoneC.text.trim().isNotEmpty
      //         ? _phoneC.text.trim()
      //         : authRef.user?.phone ?? "",
      //     "deliveryType": "pickup", //delivery
      //     "address_label": ref.read(addressVm).currentAddress,
      //     "lastname": authRef.user?.lastname ?? "",
      //     "firstname": authRef.user?.firstname ?? "",
      //     "addPaperAndGift": false,
      //     "createAccount": false,
      //     "payment": {
      //       "channel": "mobile",
      //       "medium": "Online",
      //       "amount": orderTotal.toDouble(),
      //       "reference": _reference,
      //     },
      //     "user": authRef.user?.id,
      //     "couponCode": ""
      //   });

      //   Navigator.pushNamed(context, RoutePath.orderSuccessScreen);
      // } else {
      //   FlushBarToast.fLSnackBar(
      //     snackBarType: SnackBarType.warning,
      //     message: result.message,
      //   );
      // }
    } else {
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: r.message ?? "Something went wrong",
      );
    }
  }

  double get orderTotal {
    final isDelivery =
        ref.watch(addressVm).orderDeliveryType == OrderDeliveryType.delivery;

    final subTotal = widget.cartModel.total ?? 0;

    return subTotal + (isDelivery ? deliveryFee : 0) - discountAmt;
  }

  double get deliveryFee {
    return (ref.watch(addressVm).deliveryFee?.cost ?? 0).toDouble();
  }

  double get discountAmt {
    return (widget.cartModel.total ?? 0) * (_discountValue ?? 0) / 100;
  }
}
