import 'package:flutter/material.dart';

const String images = "assets/images";

class AppImages {
  AppImages._();

  static const noImage = "$images/noImage.png";
  static const logo = "$images/logo.png";
  static const logo2 = "$images/logo2.png";
  static const logoBlack = "$images/logoBlack.png";

  // Onboarding
  static const onb0 = "$images/onboard/onb0.png";
  static const onb1 = "$images/onboard/onb1.png";
  static const onb2 = "$images/onboard/onb2.png";

  static const v0 = "$images/onboard/v0.png";
  static const v1 = "$images/onboard/v1.png";
  static const v2 = "$images/onboard/v2.png";

  static const cat = "$images/cat.png";
  static const gift = "$images/gift.png";

  static const product = "$images/product.png";
  static const coin = "$images/coin.png";

  // Emoji
  static const angry = "$images/emoji/angry.png";
  static const neutral = "$images/emoji/neutral.png";
  static const smile = "$images/emoji/smile.png";
  static const unamused = "$images/emoji/unamused.png";

  // Category
  static const all = "$images/category/all.png";
  static const beer = "$images/category/beer.png";
  static const champagne = "$images/category/champagne.png";
  static const cognac = "$images/category/cognac.png";
  static const extra = "$images/category/extra.png";
  static const spirit = "$images/category/spirit.png";
  static const ready = "$images/category/ready.png";
  static const gin = "$images/category/gin.png";
  static const liqueur = "$images/category/liqueur.png";
  static const rum = "$images/category/rum.png";
  static const tequila = "$images/category/tequila.png";
  static const vodka = "$images/category/vodka.png";
  static const water = "$images/category/water.png";
  static const whiskey = "$images/category/whiskey.png";
  static const wine = "$images/category/wine.png";

  static const groupOrder = "$images/groupOrder.png";
  static const support = "$images/support.png";
}

// Image Helper
SizedBox imageHelper(String image,
    {double? height, double? width, BoxFit? fit}) {
  return SizedBox(
      height: height,
      width: width,
      child: Image.asset(
        image,
        fit: fit,
      ));
}
