import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const transparent = Colors.transparent;
  static const black = Colors.black;
  static const white = Colors.white;
  static const grey = Colors.grey;
  static const red = Colors.red;

  static const primaryBlack = Color(0xFF222222);
  static const black70 = Color(0xFF707070);
  static const blackBD = Color(0xFFBDBDBD);
  static const blackF7 = Color(0xFFF2F4F7);
  static const black71 = Color(0xFF717171);

  static const greyF7 = Color(0xFFF7F7F7);
  static const gray500 = Color(0xFF8D91A0);
  static const grayF6 = Color(0xFFF3F4F6);
  static const grayF0 = Color(0xFFEAECF0);
  static const grayE6 = Color(0xFFE6E6E6);
  static const grayDD = Color(0xFFD0D5DD);
  static const grayD9 = Color(0xFFD9D9D9);
  static const gray75 = Color(0xFF757575);
  static const grayFA = Color(0xFFFAFAFA);
  static const grayEEE = Color(0xFFEEEEEE);
  static const gray54 = Color(0xFF344054);

  static const yellow37 = Color(0xFFD4AF37);

  static const red15 = Color(0xFFDF1515);
  static const red00 = Color(0xFFF50000);
  static const red35 = Color(0xFFEA4335);
  static const red2F = Color(0xFFD32F2F);

  // Flushbar
  static const opacityRed100 = Color(0xFFFFF8F7);
  static const greenDE = Color(0xFFEAF9DE);
  static const green2E = Color(0xFF65DE2E);
}
