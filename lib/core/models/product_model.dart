// import 'dart:convert';

// List<ProductModel> productFromJson(String str) => List<ProductModel>.from(
//     json.decode(str).map((x) => ProductModel.fromJson(x)));

// String productToJson(List<ProductModel> data) =>
//     json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

// class ProductModel {
//   final String? id;
//   final String? name;
//   final dynamic desc;
//   final dynamic category;
//   final DateTime? createdAt;
//   final DateTime? lastUpdated;
//   final List<Variation>? variations;
//   final dynamic status;

//   ProductModel({
//     this.id,
//     this.name,
//     this.desc,
//     this.category,
//     this.createdAt,
//     this.lastUpdated,
//     this.variations,
//     this.status,
//   });

//   ProductModel copyWith({
//     String? id,
//     String? name,
//     dynamic desc,
//     dynamic category,
//     DateTime? createdAt,
//     DateTime? lastUpdated,
//     List<Variation>? variations,
//     dynamic status,
//   }) =>
//       ProductModel(
//         id: id ?? this.id,
//         name: name ?? this.name,
//         desc: desc ?? this.desc,
//         category: category ?? this.category,
//         createdAt: createdAt ?? this.createdAt,
//         lastUpdated: lastUpdated ?? this.lastUpdated,
//         variations: variations ?? this.variations,
//         status: status ?? this.status,
//       );

//   factory ProductModel.fromJson(Map<String, dynamic> json) => ProductModel(
//         id: json["_id"],
//         name: json["name"],
//         desc: json["desc"],
//         category: json["category"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         lastUpdated: json["last_updated"] == null
//             ? null
//             : DateTime.parse(json["last_updated"]),
//         variations: json["variations"] == null
//             ? []
//             : List<Variation>.from(
//                 json["variations"]!.map((x) => Variation.fromJson(x))),
//         status: json["status"],
//       );

//   Map<String, dynamic> toJson() => {
//         "_id": id,
//         "name": name,
//         "desc": desc,
//         "category": category,
//         "created_at": createdAt?.toIso8601String(),
//         "last_updated": lastUpdated?.toIso8601String(),
//         "variations": variations == null
//             ? []
//             : List<dynamic>.from(variations!.map((x) => x.toJson())),
//         "status": status,
//       };
// }

// List<Variation> variationFromJson(String str) =>
//     List<Variation>.from(json.decode(str).map((x) => Variation.fromJson(x)));

// class Variation {
//   final String? name;
//   final String? productId; // for extracting all variations from products
//   final String? productName; // for extracting all variations from products
//   final String? volume;
//   final String? sku;
//   final int? unitPrice;
//   final int? cartonPrice;
//   final String? desc;
//   final String? cartonSize;
//   final String? category;
//   final String? image;
//   final String? barcode;
//   final String? status;
//   final bool? isBestSeller;
//   final bool? isRecommended;
//   final bool? isNewArrival;
//   final Discount? discount;
//   final Discounts? discounts;
//   final num? currentPrice;
//   final num? discountPercentage;
//   final Metadata? metadata;
//   final String? slug;
//   final String? id;
//   final int? stockQuantity;

//   Variation({
//     this.name,
//     this.productId,
//     this.productName,
//     this.volume,
//     this.sku,
//     this.unitPrice,
//     this.cartonPrice,
//     this.desc,
//     this.cartonSize,
//     this.category,
//     this.image,
//     this.barcode,
//     this.status,
//     this.isBestSeller,
//     this.isRecommended,
//     this.isNewArrival,
//     this.discount,
//     this.discounts,
//     this.currentPrice,
//     this.discountPercentage,
//     this.metadata,
//     this.slug,
//     this.id,
//     this.stockQuantity,
//   });

//   Variation copyWith({
//     String? name,
//     String? productId,
//     String? productName,
//     String? volume,
//     String? sku,
//     int? unitPrice,
//     int? cartonPrice,
//     String? desc,
//     String? cartonSize,
//     String? category,
//     String? image,
//     String? barcode,
//     String? status,
//     bool? isBestSeller,
//     bool? isRecommended,
//     bool? isNewArrival,
//     Discount? discount,
//     Discounts? discounts,
//     int? currentPrice,
//     int? discountPercentage,
//     Metadata? metadata,
//     String? slug,
//     String? id,
//     int? stockQuantity,
//   }) =>
//       Variation(
//         name: name ?? this.name,
//         productId: productId ?? this.productId,
//         productName: productName ?? this.productName,
//         volume: volume ?? this.volume,
//         sku: sku ?? this.sku,
//         unitPrice: unitPrice ?? this.unitPrice,
//         cartonPrice: cartonPrice ?? this.cartonPrice,
//         desc: desc ?? this.desc,
//         cartonSize: cartonSize ?? this.cartonSize,
//         category: category ?? this.category,
//         image: image ?? this.image,
//         barcode: barcode ?? this.barcode,
//         status: status ?? this.status,
//         isBestSeller: isBestSeller ?? this.isBestSeller,
//         isRecommended: isRecommended ?? this.isRecommended,
//         isNewArrival: isNewArrival ?? this.isNewArrival,
//         discount: discount ?? this.discount,
//         discounts: discounts ?? this.discounts,
//         currentPrice: currentPrice ?? this.currentPrice,
//         discountPercentage: discountPercentage ?? this.discountPercentage,
//         metadata: metadata ?? this.metadata,
//         slug: slug ?? this.slug,
//         id: id ?? this.id,
//         stockQuantity: stockQuantity ?? this.stockQuantity,
//       );

//   factory Variation.fromJson(Map<String, dynamic> json) => Variation(
//         name: json["name"],
//         volume: json["volume"],
//         sku: json["sku"],
//         unitPrice: json["unitPrice"],
//         cartonPrice: json["cartonPrice"],
//         desc: json["desc"],
//         cartonSize: json["cartonSize"],
//         category: json["category"],
//         image: json["image"],
//         barcode: json["barcode"],
//         status: json["status"],
//         isBestSeller: json["isBestSeller"],
//         isRecommended: json["isRecommended"],
//         isNewArrival: json["isNewArrival"],
//         discount: json["discount"] == null
//             ? null
//             : Discount.fromJson(json["discount"]),
//         discounts: json["discounts"] == null
//             ? null
//             : Discounts.fromJson(json["discounts"]),
//         currentPrice: json["currentPrice"],
//         metadata: json["metadata"] == null
//             ? null
//             : Metadata.fromJson(json["metadata"]),
//         discountPercentage: json["discountPercentage"],
//         slug: json["slug"],
//         id: json["_id"],
//         stockQuantity: json["stockQuantity"],
//       );

//   Map<String, dynamic> toJson() => {
//         "name": name,
//         "volume": volume,
//         "sku": sku,
//         "unitPrice": unitPrice,
//         "cartonPrice": cartonPrice,
//         "desc": desc,
//         "cartonSize": cartonSize,
//         "category": category,
//         "image": image,
//         "barcode": barcode,
//         "status": status,
//         "isBestSeller": isBestSeller,
//         "isRecommended": isRecommended,
//         "isNewArrival": isNewArrival,
//         "discount": discount,
//         "discounts": discounts?.toJson(),
//         "currentPrice": currentPrice,
//         "discountPercentage": discountPercentage,
//         "metadata": metadata?.toJson(),
//         "slug": slug,
//         "_id": id,
//         "stockQuantity": stockQuantity,
//       };
// }

// class Metadata {
//   final String? quickBooksUnitId;
//   final String? quickBooksCaseId;

//   Metadata({
//     this.quickBooksUnitId,
//     this.quickBooksCaseId,
//   });

//   Metadata copyWith({
//     String? quickBooksUnitId,
//     String? quickBooksCaseId,
//   }) =>
//       Metadata(
//         quickBooksUnitId: quickBooksUnitId ?? this.quickBooksUnitId,
//         quickBooksCaseId: quickBooksCaseId ?? this.quickBooksCaseId,
//       );

//   factory Metadata.fromJson(Map<String, dynamic> json) => Metadata(
//         quickBooksUnitId: json["quickBooksUnitId"],
//         quickBooksCaseId: json["quickBooksCaseId"],
//       );

//   Map<String, dynamic> toJson() => {
//         "quickBooksUnitId": quickBooksUnitId,
//         "quickBooksCaseId": quickBooksCaseId,
//       };
// }

// class Discount {
//   final String? discountType;
//   final int? newPrice;
//   final int? discountPercentage;
//   final DateTime? startDate;
//   final DateTime? endDate;
//   final bool? isBlackFridayDiscount;
//   final String? id;

//   Discount({
//     this.discountType,
//     this.newPrice,
//     this.discountPercentage,
//     this.startDate,
//     this.endDate,
//     this.isBlackFridayDiscount,
//     this.id,
//   });

//   factory Discount.fromJson(Map<String, dynamic> json) => Discount(
//         discountType: json["discountType"],
//         newPrice: json["newPrice"],
//         discountPercentage: json["discount_percentage"],
//         startDate: json["startDate"] == null
//             ? null
//             : DateTime.parse(json["startDate"]),
//         endDate:
//             json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
//         isBlackFridayDiscount: json["isBlackFridayDiscount"],
//         id: json["_id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "discountType": discountType,
//         "newPrice": newPrice,
//         "discount_percentage": discountPercentage,
//         "startDate": startDate?.toIso8601String(),
//         "endDate": endDate?.toIso8601String(),
//         "isBlackFridayDiscount": isBlackFridayDiscount,
//         "_id": id,
//       };
// }

// class Discounts {
//   final Case? unit;
//   final Case? discountsCase;

//   Discounts({
//     this.unit,
//     this.discountsCase,
//   });

//   factory Discounts.fromJson(Map<String, dynamic> json) => Discounts(
//         unit: json["unit"] == null ? null : Case.fromJson(json["unit"]),
//         discountsCase:
//             json["case"] == null ? null : Case.fromJson(json["case"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "unit": unit?.toJson(),
//         "case": discountsCase?.toJson(),
//       };
// }

// class Case {
//   final bool? active;
//   final num? newPrice;
//   final num? discountPercentage;
//   final DateTime? startDate;
//   final DateTime? endDate;
//   final bool? isBlackFridayDiscount;

//   Case({
//     this.active,
//     this.newPrice,
//     this.discountPercentage,
//     this.startDate,
//     this.endDate,
//     this.isBlackFridayDiscount,
//   });

//   factory Case.fromJson(Map<String, dynamic> json) => Case(
//         active: json["active"],
//         newPrice: json["newPrice"],
//         discountPercentage: json["discount_percentage"],
//         startDate: json["startDate"] == null
//             ? null
//             : DateTime.parse(json["startDate"]),
//         endDate:
//             json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
//         isBlackFridayDiscount: json["isBlackFridayDiscount"],
//       );

//   Map<String, dynamic> toJson() => {
//         "active": active,
//         "newPrice": newPrice,
//         "discount_percentage": discountPercentage,
//         "startDate": startDate?.toIso8601String(),
//         "endDate": endDate?.toIso8601String(),
//         "isBlackFridayDiscount": isBlackFridayDiscount,
//       };
// }

// To parse this JSON data, do
//
//     final productModel = productModelFromJson(jsonString);

import 'dart:convert';

List<ProductModel> productModelFromJson(String str) => List<ProductModel>.from(
    json.decode(str).map((x) => ProductModel.fromJson(x)));

String productModelToJson(List<ProductModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ProductModel {
  final DateTime? createdAt;
  final String? id;
  final String? name;
  final String? parentName;
  final String? parentId;
  final String? volume;
  final String? sku;
  final int? unitPrice;
  final int? cartonPrice;
  final String? desc;
  final String? cartonSize;
  final String? category;
  final String? image;
  final String? barcode;
  final String? status;
  final bool? isBestSeller;
  final bool? isRecommended;
  final bool? isNewArrival;
  final Discount? discount;
  final Discounts? discounts;
  final Metadata? metadata;
  final String? slug;
  final List<Variation>? variations;
  final int? sortPrice;
  final int? currentPrice;
  final num? currentCartonPrice;
  final dynamic discountPercentage;
  final int? stockQuantity;
  final String? stockStatus;

  ProductModel({
    this.createdAt,
    this.id,
    this.name,
    this.parentName,
    this.parentId,
    this.volume,
    this.sku,
    this.unitPrice,
    this.cartonPrice,
    this.desc,
    this.cartonSize,
    this.category,
    this.image,
    this.barcode,
    this.status,
    this.isBestSeller,
    this.isRecommended,
    this.isNewArrival,
    this.discount,
    this.discounts,
    this.metadata,
    this.slug,
    this.variations,
    this.sortPrice,
    this.currentPrice,
    this.currentCartonPrice,
    this.discountPercentage,
    this.stockQuantity,
    this.stockStatus,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) => ProductModel(
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        id: json["_id"],
        name: json["name"],
        parentName: json["parentName"],
        parentId: json["parentId"],
        volume: json["volume"],
        sku: json["sku"],
        unitPrice: json["unitPrice"],
        cartonPrice: json["cartonPrice"],
        desc: json["desc"],
        cartonSize: json["cartonSize"],
        category: json["category"],
        image: json["image"],
        barcode: json["barcode"],
        status: json["status"],
        isBestSeller: json["isBestSeller"],
        isRecommended: json["isRecommended"],
        isNewArrival: json["isNewArrival"],
        discount: json["discount"] == null
            ? null
            : Discount.fromJson(json["discount"]),
        discounts: json["discounts"] == null
            ? null
            : Discounts.fromJson(json["discounts"]),
        metadata: json["metadata"] == null
            ? null
            : Metadata.fromJson(json["metadata"]),
        slug: json["slug"],
        variations: json["variations"] == null
            ? []
            : List<Variation>.from(
                json["variations"]!.map((x) => Variation.fromJson(x))),
        sortPrice: json["sortPrice"],
        currentPrice: json["currentPrice"],
        currentCartonPrice: json["currentCartonPrice"],
        discountPercentage: json["discountPercentage"],
        stockQuantity: json["stockQuantity"],
        stockStatus: json["stockStatus"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt?.toIso8601String(),
        "_id": id,
        "name": name,
        "parentName": parentName,
        "parentId": parentId,
        "volume": volume,
        "sku": sku,
        "unitPrice": unitPrice,
        "cartonPrice": cartonPrice,
        "desc": desc,
        "cartonSize": cartonSize,
        "category": category,
        "image": image,
        "barcode": barcode,
        "status": status,
        "isBestSeller": isBestSeller,
        "isRecommended": isRecommended,
        "isNewArrival": isNewArrival,
        "discount": discount,
        "metadata": metadata?.toJson(),
        "slug": slug,
        "variations": variations == null
            ? []
            : List<dynamic>.from(variations!.map((x) => x.toJson())),
        "sortPrice": sortPrice,
        "currentPrice": currentPrice,
        "currentCartonPrice": currentCartonPrice,
        "discountPercentage": discountPercentage,
        "stockQuantity": stockQuantity,
        "stockStatus": stockStatus,
      };
}

class Metadata {
  final String? quickBooksUnitId;
  final String? quickBooksCaseId;

  Metadata({
    this.quickBooksUnitId,
    this.quickBooksCaseId,
  });

  factory Metadata.fromJson(Map<String, dynamic> json) => Metadata(
        quickBooksUnitId: json["quickBooksUnitId"],
        quickBooksCaseId: json["quickBooksCaseId"],
      );

  Map<String, dynamic> toJson() => {
        "quickBooksUnitId": quickBooksUnitId,
        "quickBooksCaseId": quickBooksCaseId,
      };
}

class Variation {
  final String? name;
  final String? volume;
  final String? sku;
  final int? unitPrice;
  final int? cartonPrice;
  final String? desc;
  final String? cartonSize;
  final String? category;
  final String? image;
  final String? barcode;
  final String? status;
  final bool? isBestSeller;
  final bool? isRecommended;
  final bool? isNewArrival;
  final dynamic discount;
  final Metadata? metadata;
  final String? slug;
  final String? id;

  Variation({
    this.name,
    this.volume,
    this.sku,
    this.unitPrice,
    this.cartonPrice,
    this.desc,
    this.cartonSize,
    this.category,
    this.image,
    this.barcode,
    this.status,
    this.isBestSeller,
    this.isRecommended,
    this.isNewArrival,
    this.discount,
    this.metadata,
    this.slug,
    this.id,
  });

  factory Variation.fromJson(Map<String, dynamic> json) => Variation(
        name: json["name"],
        volume: json["volume"],
        sku: json["sku"],
        unitPrice: json["unitPrice"],
        cartonPrice: json["cartonPrice"],
        desc: json["desc"],
        cartonSize: json["cartonSize"],
        category: json["category"],
        image: json["image"],
        barcode: json["barcode"],
        status: json["status"],
        isBestSeller: json["isBestSeller"],
        isRecommended: json["isRecommended"],
        isNewArrival: json["isNewArrival"],
        discount: json["discount"],
        metadata: json["metadata"] == null
            ? null
            : Metadata.fromJson(json["metadata"]),
        slug: json["slug"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "volume": volume,
        "sku": sku,
        "unitPrice": unitPrice,
        "cartonPrice": cartonPrice,
        "desc": desc,
        "cartonSize": cartonSize,
        "category": category,
        "image": image,
        "barcode": barcode,
        "status": status,
        "isBestSeller": isBestSeller,
        "isRecommended": isRecommended,
        "isNewArrival": isNewArrival,
        "discount": discount,
        "metadata": metadata?.toJson(),
        "slug": slug,
        "_id": id,
      };
}

class Discount {
  final String? discountType;
  final int? newPrice;
  final int? discountPercentage;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? isBlackFridayDiscount;
  final String? id;

  Discount({
    this.discountType,
    this.newPrice,
    this.discountPercentage,
    this.startDate,
    this.endDate,
    this.isBlackFridayDiscount,
    this.id,
  });

  factory Discount.fromJson(Map<String, dynamic> json) => Discount(
        discountType: json["discountType"],
        newPrice: json["newPrice"],
        discountPercentage: json["discount_percentage"],
        startDate: json["startDate"] == null
            ? null
            : DateTime.parse(json["startDate"]),
        endDate:
            json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
        isBlackFridayDiscount: json["isBlackFridayDiscount"],
        id: json["_id"],
      );
}

class Discounts {
  final Case? unit;
  final Case? discountsCase;

  Discounts({
    this.unit,
    this.discountsCase,
  });

  factory Discounts.fromJson(Map<String, dynamic> json) => Discounts(
        unit: json["unit"] == null ? null : Case.fromJson(json["unit"]),
        discountsCase:
            json["case"] == null ? null : Case.fromJson(json["case"]),
      );
}

class Case {
  final bool? active;
  final double? newPrice;
  final int? discountPercentage;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? isBlackFridayDiscount;

  Case({
    this.active,
    this.newPrice,
    this.discountPercentage,
    this.startDate,
    this.endDate,
    this.isBlackFridayDiscount,
  });

  factory Case.fromJson(Map<String, dynamic> json) => Case(
        active: json["active"],
        newPrice: json["newPrice"]?.toDouble(),
        discountPercentage: json["discount_percentage"],
        startDate: json["startDate"] == null
            ? null
            : DateTime.parse(json["startDate"]),
        endDate:
            json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
        isBlackFridayDiscount: json["isBlackFridayDiscount"],
      );
}
