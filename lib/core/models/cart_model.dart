import 'dart:convert';

CartModel cartModelFromJson(String str) => CartModel.fromJson(json.decode(str));

String cartModelToJson(CartModel data) => json.encode(data.toJson());

class CartModel {
  final String? id;
  final dynamic sessionId;
  final int? total;
  final bool? isWishList;
  final List<GroupOrder>? groupOrders;
  final CartItems? items;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CartModel({
    this.id,
    this.sessionId,
    this.total,
    this.isWishList,
    this.groupOrders,
    this.items,
    this.createdAt,
    this.updatedAt,
  });

  CartModel copyWith({
    String? id,
    dynamic sessionId,
    int? total,
    bool? isWishList,
    List<GroupOrder>? groupOrders,
    CartItems? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      CartModel(
        id: id ?? this.id,
        sessionId: sessionId ?? this.sessionId,
        total: total ?? this.total,
        isWishList: isWishList ?? this.isWishList,
        groupOrders: groupOrders ?? this.groupOrders,
        items: items ?? this.items,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory CartModel.fromJson(Map<String, dynamic> json) => CartModel(
        id: json["_id"],
        sessionId: json["sessionId"],
        total: json["total"],
        isWishList: json["isWishList"],
        groupOrders: json["groupOrders"] == null
            ? []
            : List<GroupOrder>.from(
                json["groupOrders"]!.map((x) => GroupOrder.fromJson(x))),
        items: json["items"] == null ? null : CartItems.fromJson(json["items"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "sessionId": sessionId,
        "total": total,
        "isWishList": isWishList,
        "groupOrders": groupOrders == null
            ? []
            : List<dynamic>.from(groupOrders!.map((x) => x.toJson())),
        "items": items?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}

class GroupOrder {
  final String? code;
  final String? name;

  GroupOrder({
    this.code,
    this.name,
  });

  GroupOrder copyWith({
    String? code,
    String? name,
  }) =>
      GroupOrder(
        code: code ?? this.code,
        name: name ?? this.name,
      );

  factory GroupOrder.fromJson(Map<String, dynamic> json) => GroupOrder(
        code: json["code"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "name": name,
      };
}

class CartItems {
  final CartProducts? specialProducts;
  final CartProducts? regularProducts;
  final int? overallTotal;

  CartItems({
    this.specialProducts,
    this.regularProducts,
    this.overallTotal,
  });

  CartItems copyWith({
    CartProducts? specialProducts,
    CartProducts? regularProducts,
    int? overallTotal,
  }) =>
      CartItems(
        specialProducts: specialProducts ?? this.specialProducts,
        regularProducts: regularProducts ?? this.regularProducts,
        overallTotal: overallTotal ?? this.overallTotal,
      );

  factory CartItems.fromJson(Map<String, dynamic> json) => CartItems(
        specialProducts: json["specialProducts"] == null
            ? null
            : CartProducts.fromJson(json["specialProducts"]),
        regularProducts: json["regularProducts"] == null
            ? null
            : CartProducts.fromJson(json["regularProducts"]),
        overallTotal: json["overallTotal"],
      );

  Map<String, dynamic> toJson() => {
        "specialProducts": specialProducts?.toJson(),
        "regularProducts": regularProducts?.toJson(),
        "overallTotal": overallTotal,
      };
}

class CartProducts {
  final List<CartItem>? items;
  final int? total;
  final int? count;

  CartProducts({
    this.items,
    this.total,
    this.count,
  });

  CartProducts copyWith({
    List<CartItem>? items,
    int? total,
    int? count,
  }) =>
      CartProducts(
        items: items ?? this.items,
        total: total ?? this.total,
        count: count ?? this.count,
      );

  factory CartProducts.fromJson(Map<String, dynamic> json) => CartProducts(
        items: json["items"] == null
            ? []
            : List<CartItem>.from(
                json["items"]!.map((x) => CartItem.fromJson(x))),
        total: json["total"],
        count: json["count"],
      );

  Map<String, dynamic> toJson() => {
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "total": total,
        "count": count,
      };
}

class CartItem {
  final String? id;
  final String? productId;
  final String? variationId;
  final String? productName;
  final String? variationName;
  final int? quantity;
  final int? price;
  final double? pricePerUnit;
  final String? image;
  final String? category;
  final int? cartonPrice;
  final String? cartonSize;

  CartItem({
    this.id,
    this.productId,
    this.variationId,
    this.productName,
    this.variationName,
    this.quantity,
    this.price,
    this.pricePerUnit,
    this.image,
    this.category,
    this.cartonPrice,
    this.cartonSize,
  });

  CartItem copyWith({
    String? id,
    String? productId,
    String? variationId,
    String? productName,
    String? variationName,
    int? quantity,
    int? price,
    double? pricePerUnit,
    String? image,
    String? category,
    int? cartonPrice,
    String? cartonSize,
  }) =>
      CartItem(
        id: id ?? this.id,
        productId: productId ?? this.productId,
        variationId: variationId ?? this.variationId,
        productName: productName ?? this.productName,
        variationName: variationName ?? this.variationName,
        quantity: quantity ?? this.quantity,
        price: price ?? this.price,
        pricePerUnit: pricePerUnit ?? this.pricePerUnit,
        image: image ?? this.image,
        category: category ?? this.category,
        cartonPrice: cartonPrice ?? this.cartonPrice,
        cartonSize: cartonSize ?? this.cartonSize,
      );

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
        id: json["_id"],
        productId: json["productId"],
        variationId: json["variationId"],
        productName: json["productName"],
        variationName: json["variationName"],
        quantity: json["quantity"],
        price: json["price"],
        pricePerUnit: json["pricePerUnit"]?.toDouble(),
        image: json["image"],
        category: json["category"],
        cartonPrice: json["cartonPrice"],
        cartonSize: json["cartonSize"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "productId": productId,
        "variationId": variationId,
        "productName": productName,
        "variationName": variationName,
        "quantity": quantity,
        "price": price,
        "pricePerUnit": pricePerUnit,
        "image": image,
        "category": category,
        "cartonPrice": cartonPrice,
        "cartonSize": cartonSize,
      };
}
