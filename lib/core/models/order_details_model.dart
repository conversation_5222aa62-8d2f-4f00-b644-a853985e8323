import 'dart:convert';

import 'package:bottle_king_mobile/core/models/models.dart';

OrderDetailsModel orderDetailsModelFromJson(String str) =>
    OrderDetailsModel.fromJson(json.decode(str));

String orderDetailsModelToJson(OrderDetailsModel data) =>
    json.encode(data.toJson());

class OrderDetailsModel {
  final String? id;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final String? orderId;
  final bool? firstUserOrder;
  final String? addressLabel;
  final bool? addPaper;
  final String? deliveryNote;
  final String? deliveryType;
  final int? fare;
  final Customer? customer;
  final String? status;
  final bool? paymentStatus;
  final List<Product>? products;
  final Payment? payment;
  final dynamic coupon;
  final bool? csatEmailSent;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final int? v;
  final OrderStatus? orderStatus;
  final Payment? paymentDetails;
  final DeliveryDetails? deliveryDetails;
  final List<ProductDetail>? productDetails;
  final CustomerDetails? customerDetails;

  OrderDetailsModel({
    this.id,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.orderId,
    this.firstUserOrder,
    this.addressLabel,
    this.addPaper,
    this.deliveryNote,
    this.deliveryType,
    this.fare,
    this.customer,
    this.status,
    this.paymentStatus,
    this.products,
    this.payment,
    this.coupon,
    this.csatEmailSent,
    this.createdAt,
    this.lastUpdated,
    this.v,
    this.orderStatus,
    this.paymentDetails,
    this.deliveryDetails,
    this.productDetails,
    this.customerDetails,
  });

  factory OrderDetailsModel.fromJson(Map<String, dynamic> json) =>
      OrderDetailsModel(
        id: json["_id"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        email: json["email"],
        phone: json["phone"],
        orderId: json["order_id"],
        firstUserOrder: json["firstUserOrder"],
        addressLabel: json["address_label"],
        addPaper: json["addPaper"],
        deliveryNote: json["deliveryNote"],
        deliveryType: json["deliveryType"],
        fare: json["fare"],
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        status: json["status"],
        paymentStatus: json["payment_status"],
        products: json["products"] == null
            ? []
            : List<Product>.from(
                json["products"]!.map((x) => Product.fromJson(x))),
        payment:
            json["payment"] == null ? null : Payment.fromJson(json["payment"]),
        coupon: json["coupon"],
        csatEmailSent: json["csatEmailSent"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        v: json["__v"],
        orderStatus: json["orderStatus"] == null
            ? null
            : OrderStatus.fromJson(json["orderStatus"]),
        paymentDetails: json["paymentDetails"] == null
            ? null
            : Payment.fromJson(json["paymentDetails"]),
        deliveryDetails: json["deliveryDetails"] == null
            ? null
            : DeliveryDetails.fromJson(json["deliveryDetails"]),
        productDetails: json["productDetails"] == null
            ? []
            : List<ProductDetail>.from(
                json["productDetails"]!.map((x) => ProductDetail.fromJson(x))),
        customerDetails: json["customerDetails"] == null
            ? null
            : CustomerDetails.fromJson(json["customerDetails"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "firstname": firstname,
        "lastname": lastname,
        "email": email,
        "phone": phone,
        "order_id": orderId,
        "firstUserOrder": firstUserOrder,
        "address_label": addressLabel,
        "addPaper": addPaper,
        "deliveryNote": deliveryNote,
        "deliveryType": deliveryType,
        "fare": fare,
        "customer": customer?.toJson(),
        "status": status,
        "payment_status": paymentStatus,
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
        "payment": payment?.toJson(),
        "coupon": coupon,
        "csatEmailSent": csatEmailSent,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "__v": v,
        "orderStatus": orderStatus?.toJson(),
        "paymentDetails": paymentDetails?.toJson(),
        "deliveryDetails": deliveryDetails?.toJson(),
        "productDetails": productDetails == null
            ? []
            : List<dynamic>.from(productDetails!.map((x) => x.toJson())),
        "customerDetails": customerDetails?.toJson(),
      };
}

class Customer {
  final String? id;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final int? points;

  Customer({
    this.id,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.points,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        id: json["_id"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        email: json["email"],
        phone: json["phone"],
        points: json["points"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "firstname": firstname,
        "lastname": lastname,
        "email": email,
        "phone": phone,
        "points": points,
      };
}

class CustomerDetails {
  final String? name;
  final String? email;
  final String? phone;
  final int? points;

  CustomerDetails({
    this.name,
    this.email,
    this.phone,
    this.points,
  });

  factory CustomerDetails.fromJson(Map<String, dynamic> json) =>
      CustomerDetails(
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        points: json["points"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "phone": phone,
        "points": points,
      };
}

class DeliveryDetails {
  final String? type;
  final String? address;
  final int? fare;

  DeliveryDetails({
    this.type,
    this.address,
    this.fare,
  });

  factory DeliveryDetails.fromJson(Map<String, dynamic> json) =>
      DeliveryDetails(
        type: json["type"],
        address: json["address"],
        fare: json["fare"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "address": address,
        "fare": fare,
      };
}

class OrderStatus {
  final String? current;
  final String? label;
  final DateTime? timestamp;

  OrderStatus({
    this.current,
    this.label,
    this.timestamp,
  });

  factory OrderStatus.fromJson(Map<String, dynamic> json) => OrderStatus(
        current: json["current"],
        label: json["label"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "current": current,
        "label": label,
        "timestamp": timestamp?.toIso8601String(),
      };
}

class ProductDetail {
  final String? name;
  final String? variation;
  final int? quantity;
  final int? unitPrice;
  final int? total;
  final String? image;

  ProductDetail({
    this.name,
    this.variation,
    this.quantity,
    this.unitPrice,
    this.total,
    this.image,
  });

  factory ProductDetail.fromJson(Map<String, dynamic> json) => ProductDetail(
        name: json["name"],
        variation: json["variation"],
        quantity: json["quantity"],
        unitPrice: json["unitPrice"],
        total: json["total"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "variation": variation,
        "quantity": quantity,
        "unitPrice": unitPrice,
        "total": total,
        "image": image,
      };
}
