import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  static Future<void> initialize(String flavor) async {
    // final flavor = const String.fromEnvironment('FLAVOR', defaultValue: 'prod');
    final envFile = '.env.$flavor';

    try {
      // Load the appropriate .env file based on flavor
      await dotenv.load(fileName: envFile);
      printty('Loaded $envFile');

      // Set environment
      EnvironmentConfig.environment = Environment.values.firstWhere(
        (e) => e.name == flavor,
        orElse: () => throw Exception('Unknown flavor: $flavor'),
      );
    } on Exception catch (e) {
      printty('Error initializing environment: $e');
      rethrow;
    }
  }

  // Environment variables
  static String get baseUrl => dotenv.env['BASE_URL'] ?? '';
  static String get googlePlacesApiKey =>
      dotenv.env['GOOGLE_PLACES_API_KEY'] ?? '';
  static String get googleMapApiKey => dotenv.env['GOOGLE_MAP_API_KEY'] ?? '';
  static String get paystackKey => dotenv.env['PAYSTACK_KEY'] ?? '';
  static String get paystackSecretKey =>
      dotenv.env['PAYSTACK_SCREET_KEY'] ?? '';
  // static String get testPROD => dotenv.env['TEST_PROD'] ?? '';
}

// flutter build apk --flavor dev --target lib/main_dev.dart
// flutter build apk --flavor staging --target lib/main_staging.dart
// flutter build apk --flavor prod --target lib/main_prod.dart

// flutter build appbundle --flavor dev -t lib/main_dev.dart
// flutter build appbundle --flavor staging -t lib/main_staging.dart
// flutter build appbundle --flavor prod -t lib/main_prod.dart
