import 'package:bottle_king_mobile/core/core.dart';

void handleApiResponse({
  required ApiResponse response,
  String? successMsg,
  bool showSuccessMessage = true,
  bool showErrorMessage = true,
  void Function()? onSuccess,
  void Function()? onError,
}) {
  if (response.success) {
    if (onSuccess != null) onSuccess();
    if (showSuccessMessage) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.success,
          message: successMsg ?? response.message ?? 'Operation successful');
    }
  } else {
    if (onError != null) onError();
    if (showErrorMessage) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message: response.message ?? 'Something went wrong');
    }
  }
}
